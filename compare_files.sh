#!/bin/bash

# Script para comparar si dos archivos tienen las mismas líneas (sin importar el orden)

usage() {
    echo "Uso: $0 <archivo1> <archivo2> [opciones]"
    echo ""
    echo "Opciones:"
    echo "  -i, --ignore-case    Ignorar diferencias entre mayúsculas y minúsculas"
    echo "  -e, --ignore-empty   Ignorar líneas vacías"
    echo "  -q, --quiet          Solo mostrar resultado (0=iguales, 1=diferentes)"
    echo "  -h, --help           Mostrar esta ayuda"
    echo ""
    echo "Ejemplos:"
    echo "  $0 file1.txt file2.txt"
    echo "  $0 file1.txt file2.txt --ignore-case"
    echo "  $0 file1.txt file2.txt -q"
    exit 1
}

# Valores por defecto
IGNORE_CASE=false
IGNORE_EMPTY=false
QUIET=false

# Procesar argumentos
while [[ $# -gt 0 ]]; do
    case $1 in
        -i|--ignore-case)
            IGNORE_CASE=true
            shift
            ;;
        -e|--ignore-empty)
            IGNORE_EMPTY=true
            shift
            ;;
        -q|--quiet)
            QUIET=true
            shift
            ;;
        -h|--help)
            usage
            ;;
        -*)
            echo "Opción desconocida: $1"
            usage
            ;;
        *)
            if [ -z "$FILE1" ]; then
                FILE1="$1"
            elif [ -z "$FILE2" ]; then
                FILE2="$1"
            else
                echo "Demasiados argumentos"
                usage
            fi
            shift
            ;;
    esac
done

# Verificar que se proporcionaron ambos archivos
if [ -z "$FILE1" ] || [ -z "$FILE2" ]; then
    echo "Error: Debes proporcionar dos archivos para comparar"
    usage
fi

# Verificar que los archivos existen
if [ ! -f "$FILE1" ]; then
    echo "Error: El archivo '$FILE1' no existe"
    exit 1
fi

if [ ! -f "$FILE2" ]; then
    echo "Error: El archivo '$FILE2' no existe"
    exit 1
fi

# Crear archivos temporales para procesar
TEMP1=$(mktemp)
TEMP2=$(mktemp)

# Función de limpieza
cleanup() {
    rm -f "$TEMP1" "$TEMP2"
}
trap cleanup EXIT

# Procesar archivo 1
if [ "$IGNORE_EMPTY" = true ]; then
    grep -v '^[[:space:]]*$' "$FILE1" > "$TEMP1"
else
    cp "$FILE1" "$TEMP1"
fi

if [ "$IGNORE_CASE" = true ]; then
    tr '[:upper:]' '[:lower:]' < "$TEMP1" > "${TEMP1}.tmp" && mv "${TEMP1}.tmp" "$TEMP1"
fi

# Procesar archivo 2
if [ "$IGNORE_EMPTY" = true ]; then
    grep -v '^[[:space:]]*$' "$FILE2" > "$TEMP2"
else
    cp "$FILE2" "$TEMP2"
fi

if [ "$IGNORE_CASE" = true ]; then
    tr '[:upper:]' '[:lower:]' < "$TEMP2" > "${TEMP2}.tmp" && mv "${TEMP2}.tmp" "$TEMP2"
fi

# Ordenar ambos archivos y comparar
sort "$TEMP1" > "${TEMP1}.sorted"
sort "$TEMP2" > "${TEMP2}.sorted"

if [ "$QUIET" = true ]; then
    if diff -q "${TEMP1}.sorted" "${TEMP2}.sorted" > /dev/null; then
        echo "IGUALES"
        exit 0
    else
        echo "DIFERENTES"
        exit 1
    fi
else
    echo "=== Comparación de archivos ==="
    echo "Archivo 1: $FILE1"
    echo "Archivo 2: $FILE2"
    echo "================================"
    
    # Contar líneas
    LINES1=$(wc -l < "$TEMP1")
    LINES2=$(wc -l < "$TEMP2")
    UNIQUE1=$(sort "$TEMP1" | uniq | wc -l)
    UNIQUE2=$(sort "$TEMP2" | uniq | wc -l)
    
    echo "📊 Estadísticas:"
    echo "  - Líneas en archivo 1: $LINES1"
    echo "  - Líneas en archivo 2: $LINES2"
    echo "  - Líneas únicas en archivo 1: $UNIQUE1"
    echo "  - Líneas únicas en archivo 2: $UNIQUE2"
    
    if diff -q "${TEMP1}.sorted" "${TEMP2}.sorted" > /dev/null; then
        echo ""
        echo "✅ Los archivos tienen las mismas líneas (mismo contenido)"
        exit 0
    else
        echo ""
        echo "❌ Los archivos NO tienen las mismas líneas"
        echo ""
        echo "📝 Diferencias detalladas:"
        diff "${TEMP1}.sorted" "${TEMP2}.sorted" | head -20
        
        # Contar diferencias
        DIFF_COUNT=$(diff "${TEMP1}.sorted" "${TEMP2}.sorted" | wc -l)
        if [ "$DIFF_COUNT" -gt 20 ]; then
            echo "... (mostrando solo las primeras 20 diferencias de $DIFF_COUNT total)"
        fi
        
        exit 1
    fi
fi
