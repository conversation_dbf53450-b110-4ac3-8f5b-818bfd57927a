# Scripts para Comparar Archivos de Texto

Este directorio contiene scripts para comparar si dos archivos de texto tienen las mismas líneas, independientemente del orden en que aparezcan.

## Scripts Disponibles

### 1. `compare_files.py` (Python)
Script más completo con múltiples opciones y salida detallada.

#### Uso Básico
```bash
python compare_files.py archivo1.txt archivo2.txt
```

#### Opciones Disponibles
- `--case-insensitive`: Ignorar diferencias entre mayúsculas y minúsculas
- `--include-empty`: Incluir líneas vacías en la comparación (por defecto se ignoran)
- `--quiet`: Solo mostrar el resultado (IGUALES/DIFERENTES)

#### Ejemplos
```bash
# Comparación básica
python compare_files.py test_file1.txt test_file2.txt

# Ignorar mayúsculas/minúsculas
python compare_files.py archivo1.txt archivo2.txt --case-insensitive

# Modo silencioso (útil para scripts)
python compare_files.py archivo1.txt archivo2.txt --quiet

# Incluir líneas vacías
python compare_files.py archivo1.txt archivo2.txt --include-empty
```

### 2. `compare_files.sh` (Bash)
Script más simple para sistemas Unix/Linux.

#### Uso Básico
```bash
./compare_files.sh archivo1.txt archivo2.txt
```

#### Opciones Disponibles
- `-i, --ignore-case`: Ignorar diferencias entre mayúsculas y minúsculas
- `-e, --ignore-empty`: Ignorar líneas vacías
- `-q, --quiet`: Solo mostrar resultado
- `-h, --help`: Mostrar ayuda

## Códigos de Salida

Ambos scripts devuelven códigos de salida estándar:
- **0**: Los archivos tienen las mismas líneas
- **1**: Los archivos son diferentes o hubo un error

Esto los hace útiles para usar en scripts automatizados:

```bash
# En bash
if python compare_files.py file1.txt file2.txt --quiet; then
    echo "Los archivos son iguales"
else
    echo "Los archivos son diferentes"
fi
```

## Ejemplos de Salida

### Archivos Iguales
```
=== Comparación de archivos ===
Archivo 1: test_file1.txt
Archivo 2: test_file2.txt
================================
📊 Estadísticas:
  - Líneas en archivo 1: 5
  - Líneas en archivo 2: 5
  - Líneas únicas en archivo 1: 5
  - Líneas únicas en archivo 2: 5

✅ Los archivos tienen las mismas líneas (mismo contenido)
```

### Archivos Diferentes
```
=== Comparación de archivos ===
Archivo 1: test_file1.txt
Archivo 2: test_file3.txt
================================
📊 Estadísticas:
  - Líneas en archivo 1: 5
  - Líneas en archivo 2: 5
  - Líneas únicas en archivo 1: 5
  - Líneas únicas en archivo 2: 5

❌ Los archivos NO tienen las mismas líneas

📝 Líneas que solo están en 'test_file1.txt':
  - 'fresa' (aparece 1 vez/veces)

📝 Líneas que solo están en 'test_file3.txt':
  - 'uva' (aparece 1 vez/veces)
```

## Casos de Uso

1. **Validación de resultados**: Comparar archivos de salida de diferentes ejecuciones
2. **Testing**: Verificar que los resultados de pruebas sean consistentes
3. **Datasets**: Comparar datasets que pueden estar en diferente orden
4. **Logs**: Comparar archivos de log donde el orden puede variar

## Archivos de Prueba

Se incluyen archivos de ejemplo:
- `test_file1.txt`: Lista de frutas en un orden
- `test_file2.txt`: Las mismas frutas en diferente orden
- `test_file3.txt`: Lista similar pero con una fruta diferente

## Requisitos

- **Python**: Python 3.6 o superior (para `compare_files.py`)
- **Bash**: Para sistemas Unix/Linux (para `compare_files.sh`)

## Notas

- Por defecto, ambos scripts ignoran líneas vacías
- La comparación es sensible a mayúsculas/minúsculas por defecto
- Los scripts manejan archivos con codificación UTF-8
- Se eliminan espacios en blanco al inicio y final de cada línea
