#!/usr/bin/env python3
"""
Script para comparar si dos archivos de texto tienen las mismas líneas,
independientemente del orden en que aparezcan.
"""

import sys
import argparse
from collections import Counter
from pathlib import Path


def read_lines(file_path):
    """
    Lee todas las líneas de un archivo y las devuelve como una lista.
    Elimina espacios en blanco al inicio y final de cada línea.
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            # Eliminar espacios en blanco y líneas vacías
            lines = [line.strip() for line in f.readlines()]
            # Filtrar líneas vacías si se desea
            return [line for line in lines if line]
    except FileNotFoundError:
        print(f"Error: No se pudo encontrar el archivo '{file_path}'")
        return None
    except Exception as e:
        print(f"Error al leer el archivo '{file_path}': {e}")
        return None


def compare_files(file1_path, file2_path, ignore_empty_lines=True, case_sensitive=True):
    """
    Compara dos archivos para verificar si tienen las mismas líneas.
    
    Args:
        file1_path: Ruta al primer archivo
        file2_path: Ruta al segundo archivo
        ignore_empty_lines: Si True, ignora las líneas vacías
        case_sensitive: Si True, la comparación es sensible a mayúsculas/minúsculas
    
    Returns:
        dict: Resultado de la comparación con detalles
    """
    # Leer archivos
    lines1 = read_lines(file1_path)
    lines2 = read_lines(file2_path)
    
    if lines1 is None or lines2 is None:
        return {"success": False, "error": "No se pudieron leer los archivos"}
    
    # Procesar líneas según opciones
    if not case_sensitive:
        lines1 = [line.lower() for line in lines1]
        lines2 = [line.lower() for line in lines2]
    
    if ignore_empty_lines:
        lines1 = [line for line in lines1 if line.strip()]
        lines2 = [line for line in lines2 if line.strip()]
    
    # Usar Counter para comparar frecuencias de líneas
    counter1 = Counter(lines1)
    counter2 = Counter(lines2)
    
    # Verificar si son iguales
    are_equal = counter1 == counter2
    
    # Calcular diferencias
    only_in_file1 = counter1 - counter2
    only_in_file2 = counter2 - counter1
    
    result = {
        "success": True,
        "are_equal": are_equal,
        "file1_lines": len(lines1),
        "file2_lines": len(lines2),
        "unique_lines_file1": len(set(lines1)),
        "unique_lines_file2": len(set(lines2)),
        "only_in_file1": dict(only_in_file1),
        "only_in_file2": dict(only_in_file2)
    }
    
    return result


def print_results(result, file1_path, file2_path):
    """Imprime los resultados de la comparación de forma legible."""
    print(f"\n=== Comparación de archivos ===")
    print(f"Archivo 1: {file1_path}")
    print(f"Archivo 2: {file2_path}")
    print(f"{'='*50}")
    
    if not result["success"]:
        print(f"❌ Error: {result.get('error', 'Error desconocido')}")
        return
    
    print(f"📊 Estadísticas:")
    print(f"  - Líneas en archivo 1: {result['file1_lines']}")
    print(f"  - Líneas en archivo 2: {result['file2_lines']}")
    print(f"  - Líneas únicas en archivo 1: {result['unique_lines_file1']}")
    print(f"  - Líneas únicas en archivo 2: {result['unique_lines_file2']}")
    
    if result["are_equal"]:
        print(f"\n✅ Los archivos tienen las mismas líneas (mismo contenido)")
    else:
        print(f"\n❌ Los archivos NO tienen las mismas líneas")
        
        if result["only_in_file1"]:
            print(f"\n📝 Líneas que solo están en '{file1_path}':")
            for line, count in result["only_in_file1"].items():
                print(f"  - '{line}' (aparece {count} vez/veces)")
        
        if result["only_in_file2"]:
            print(f"\n📝 Líneas que solo están en '{file2_path}':")
            for line, count in result["only_in_file2"].items():
                print(f"  - '{line}' (aparece {count} vez/veces)")


def main():
    parser = argparse.ArgumentParser(
        description="Compara si dos archivos de texto tienen las mismas líneas, independientemente del orden"
    )
    parser.add_argument("file1", help="Ruta al primer archivo")
    parser.add_argument("file2", help="Ruta al segundo archivo")
    parser.add_argument(
        "--case-insensitive", 
        action="store_true", 
        help="Ignorar diferencias entre mayúsculas y minúsculas"
    )
    parser.add_argument(
        "--include-empty", 
        action="store_true", 
        help="Incluir líneas vacías en la comparación"
    )
    parser.add_argument(
        "--quiet", 
        action="store_true", 
        help="Solo mostrar el resultado (igual/diferente)"
    )
    
    args = parser.parse_args()
    
    # Verificar que los archivos existen
    if not Path(args.file1).exists():
        print(f"Error: El archivo '{args.file1}' no existe")
        sys.exit(1)
    
    if not Path(args.file2).exists():
        print(f"Error: El archivo '{args.file2}' no existe")
        sys.exit(1)
    
    # Realizar comparación
    result = compare_files(
        args.file1, 
        args.file2,
        ignore_empty_lines=not args.include_empty,
        case_sensitive=not args.case_insensitive
    )
    
    if args.quiet:
        if result["success"] and result["are_equal"]:
            print("IGUALES")
            sys.exit(0)
        else:
            print("DIFERENTES")
            sys.exit(1)
    else:
        print_results(result, args.file1, args.file2)
        
        # Exit code para scripts
        if result["success"] and result["are_equal"]:
            sys.exit(0)
        else:
            sys.exit(1)


if __name__ == "__main__":
    main()
